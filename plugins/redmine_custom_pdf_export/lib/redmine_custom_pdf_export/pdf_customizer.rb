module RedmineCustomPdfExport
  module PdfCustomizer
    # Custom PDF generation for single issue
    def issue_to_pdf(issue, assoc={})
      settings = Setting.plugin_redmine_custom_pdf_export || {}

      # Check if custom PDF export is enabled and configured
      if should_use_custom_pdf?(settings)
        generate_custom_pdf(issue, assoc, settings)
      else
        # Fall back to original PDF generation
        super
      end
    end

    def should_use_custom_pdf?(settings)
      # Use custom PDF if any custom settings are configured
      settings['company_name'].present? ||
      settings['company_logo_url'].present? ||
      settings['export_mode'] == 'summary' ||
      settings['enable_signature_section'] == true ||
      settings['custom_memo_text'].present?
    end

    def generate_custom_pdf(issue, assoc, settings)
      pdf = Redmine::Export::PDF::ITCPDF.new(current_language)
      pdf.set_title("#{issue.project} - #{issue.tracker} ##{issue.id}")
      pdf.alias_nb_pages
      pdf.footer_date = format_date(User.current.today)

      # Add custom header if configured
      add_custom_header(pdf, issue, settings)

      pdf.add_page

      # Generate content based on export mode
      if settings['export_mode'] == 'summary'
        generate_summary_content(pdf, issue, settings)
      else
        generate_detailed_content(pdf, issue, assoc, settings)
      end

      # Add custom signature section if enabled
      if settings['enable_signature_section']
        add_signature_section(pdf, issue, settings)
      end

      pdf.output
    end

    private

    def add_custom_header(pdf, issue, settings)
      return if settings['company_name'].blank? && settings['company_logo_url'].blank?

      # Override the default header
      pdf.set_print_header(true)
      pdf.set_margins(10, 25, 10)

      # Add logo on the right if present
      if settings['company_logo_url'].present?
        # Convert URL to local file path if needed
        logo_path = if settings['company_logo_url'].start_with?('http')
          # Download and save logo to temp directory
          require 'open-uri'
          require 'tempfile'
          temp_file = Tempfile.new(['logo', File.extname(settings['company_logo_url'])])
          temp_file.binmode
          temp_file.write(URI.open(settings['company_logo_url']).read)
          temp_file.close
          temp_file.path
        else
          settings['company_logo_url']
        end

        # Set header data without logo since we're adding it directly
        if settings['copyright_text'].present?
          pdf.set_header_data(
            logo_path, # No logo in header
            30,
            settings['company_name'].to_s + ' - ' + settings['copyright_text'].to_s, # Company name
            ''
          )
        else
          pdf.set_header_data(
            logo_path, # No logo in header
            30,
            settings['company_name'].to_s, # Company name
            ''
          )
        end

        # Ensure header is printed
        pdf.set_print_header(true)
      else
        if settings['company_name'].present?
          if settings['copyright_text'].present?
          pdf.set_header_data(
            '',
              0,
              settings['company_name'].to_s + ' - ' + settings['copyright_text'].to_s,
              ''
            )
          else
            pdf.set_header_data(
              '',
              0,
              settings['company_name'].to_s,
              ''
            )
          end
        end
      end
    end

    def generate_summary_content(pdf, issue, settings)
      # Summary mode - only key information
      pdf.SetFontStyle('B', 14)
      pdf.RDMMultiCell(190, 8, "#{issue.tracker} ##{issue.id}: #{issue.subject}")
      pdf.ln(5)

      # Basic information table
      pdf.SetFontStyle('B', 10)
      pdf.RDMCell(40, 6, l(:field_status) + ':', 'LT', 0)
      pdf.SetFontStyle('', 10)
      pdf.RDMCell(60, 6, issue.status.to_s, 'RT', 0)
      pdf.SetFontStyle('B', 10)
      pdf.RDMCell(40, 6, l(:field_priority) + ':', 'LT', 0)
      pdf.SetFontStyle('', 10)
      pdf.RDMCell(50, 6, issue.priority.to_s, 'RT', 1)

      pdf.SetFontStyle('B', 10)
      pdf.RDMCell(40, 6, l(:field_assigned_to) + ':', 'LB', 0)
      pdf.SetFontStyle('', 10)
      pdf.RDMCell(60, 6, issue.assigned_to.to_s, 'RB', 0)
      pdf.SetFontStyle('B', 10)
      pdf.RDMCell(40, 6, l(:field_due_date) + ':', 'LB', 0)
      pdf.SetFontStyle('', 10)
      pdf.RDMCell(50, 6, format_date(issue.due_date), 'RB', 1)

      pdf.ln(5)

      # Description (truncated)
      if issue.description.present?
        pdf.SetFontStyle('B', 10)
        pdf.RDMCell(190, 6, l(:field_description) + ':', 'LRT', 1)
        pdf.SetFontStyle('', 9)
        description = strip_tags(issue.description).truncate(500)
        pdf.RDMMultiCell(190, 5, description, 'LRB')
      end

      # Custom memo if configured
      if settings['custom_memo_text'].present?
        pdf.ln(5)
        pdf.SetFontStyle('B', 10)
        pdf.RDMCell(190, 6, l(:label_custom_memo, :default => 'Memo') + ':', 'LRT', 1)
        pdf.SetFontStyle('', 9)
        pdf.RDMMultiCell(190, 5, settings['custom_memo_text'], 'LRB')
      end
    end

    def generate_detailed_content(pdf, issue, assoc, settings)
      # Use the original detailed content generation
      # This is a simplified version - in practice, you'd copy the original logic
      # and modify it as needed

      pdf.SetFontStyle('B', 11)
      buf = "#{issue.project} - #{issue.tracker} ##{issue.id}"
      pdf.RDMMultiCell(190, 5, buf)
      pdf.SetFontStyle('', 8)

      # Issue hierarchy
      base_x = pdf.get_x
      i = 1
      issue.ancestors.visible.each do |ancestor|
        pdf.set_x(base_x + i)
        buf = "#{ancestor.tracker} # #{ancestor.id} (#{ancestor.status}): #{ancestor.subject}"
        pdf.RDMMultiCell(190 - i, 5, buf)
        i += 1 if i < 35
      end

      pdf.SetFontStyle('B', 11)
      pdf.RDMMultiCell(190 - i, 5, issue.subject.to_s)
      pdf.SetFontStyle('', 8)
      pdf.RDMMultiCell(190, 5, "#{format_time(issue.created_on)} - #{issue.author}")
      pdf.ln

      # Detailed fields (similar to original but customizable)
      add_issue_fields(pdf, issue)

      # Description
      if issue.description.present?
        pdf.SetFontStyle('B', 9)
        pdf.RDMCell(190, 5, l(:field_description), "LRT", 1)
        pdf.SetFontStyle('', 9)
        pdf.set_image_scale(1.6)
        text = pdf_format_text(issue, :description)
        pdf.RDMwriteFormattedCell(190, 5, '', '', text, issue.attachments, "LRB")
      end

      # Include journals (history) if in detailed mode and available
      if assoc[:journals].present?
        pdf.SetFontStyle('B', 9)
        pdf.RDMCell(190, 5, l(:label_history), "B")
        pdf.ln
        assoc[:journals].each do |journal|
          pdf.SetFontStyle('B', 8)
          title = "##{journal.indice} - #{format_time(journal.created_on)} - #{journal.user}"
          title += " (#{l(:field_private_notes)})" if journal.private_notes?
          pdf.RDMCell(190, 5, title)
          pdf.ln
          pdf.SetFontStyle('I', 8)
          details_to_strings(journal.visible_details, true).each do |string|
            pdf.RDMMultiCell(190, 5, "- " + string)
          end
          if journal.notes?
            pdf.ln unless journal.details.empty?
            pdf.SetFontStyle('', 8)
            text = pdf_format_text(journal, :notes)
            pdf.RDMwriteFormattedCell(190, 5, '', '', text, issue.attachments, "")
          end
          pdf.ln
        end
      end

      # Custom memo if configured
      if settings['custom_memo_text'].present?
        pdf.ln(5)
        pdf.SetFontStyle('B', 9)
        pdf.RDMCell(190, 5, l(:label_custom_memo, :default => 'Memo') + ':', 'LRT', 1)
        pdf.SetFontStyle('', 9)
        pdf.RDMMultiCell(190, 5, settings['custom_memo_text'], 'LRB')
      end
    end

    def add_issue_fields(pdf, issue)
      left = []
      left << [l(:field_status), issue.status]
      left << [l(:field_priority), issue.priority]
      left << [l(:field_assigned_to), issue.assigned_to] unless issue.disabled_core_fields.include?('assigned_to_id')
      left << [l(:field_category), issue.category] unless issue.disabled_core_fields.include?('category_id')
      left << [l(:field_fixed_version), issue.fixed_version] unless issue.disabled_core_fields.include?('fixed_version_id')

      right = []
      right << [l(:field_start_date), format_date(issue.start_date)] unless issue.disabled_core_fields.include?('start_date')
      right << [l(:field_due_date), format_date(issue.due_date)] unless issue.disabled_core_fields.include?('due_date')
      right << [l(:field_done_ratio), "#{issue.done_ratio}%"] unless issue.disabled_core_fields.include?('done_ratio')
      right << [l(:field_estimated_hours), l_hours(issue.estimated_hours)] unless issue.disabled_core_fields.include?('estimated_hours')
      right << [l(:label_spent_time), l_hours(issue.total_spent_hours)] if User.current.allowed_to?(:view_time_entries, issue.project)

      # Render the fields table
      rows = [left.size, right.size].max
      left  << nil while left.size  < rows
      right << nil while right.size < rows

      base_x = pdf.get_x
      rows.times do |i|
        heights = []
        pdf.SetFontStyle('B', 9)
        item = left[i]
        heights << pdf.get_string_height(35, item ? "#{item.first}:" : "")
        item = right[i]
        heights << pdf.get_string_height(35, item ? "#{item.first}:" : "")
        pdf.SetFontStyle('', 9)
        item = left[i]
        heights << pdf.get_string_height(60, item ? item.last.to_s  : "")
        item = right[i]
        heights << pdf.get_string_height(60, item ? item.last.to_s  : "")
        height = heights.max

        item = left[i]
        pdf.SetFontStyle('B', 9)
        pdf.RDMMultiCell(35, height, item ? "#{item.first}:" : "", (i == 0 ? 'LT' : 'L'), '', 0, 0)
        pdf.SetFontStyle('', 9)
        pdf.RDMMultiCell(60, height, item ? item.last.to_s : "", (i == 0 ? 'RT' : 'R'), '', 0, 0)

        item = right[i]
        pdf.SetFontStyle('B', 9)
        pdf.RDMMultiCell(35, height, item ? "#{item.first}:" : "", (i == 0 ? 'LT' : 'L'), '', 0, 0)
        pdf.SetFontStyle('', 9)
        pdf.RDMMultiCell(60, height, item ? item.last.to_s : "", (i == 0 ? 'RT' : 'R'), '', 0, 2)

        pdf.set_x(base_x)
      end
    end

    def add_signature_section(pdf, issue, settings)
      pdf.ln(10)

      # Signature section title
      pdf.SetFontStyle('B', 12)
      pdf.RDMCell(190, 8, l(:label_signature_section, :default => 'Signature Section'), 'B', 1, 'C')
      pdf.ln(5)

      # Get stakeholders for signature
      stakeholders = get_issue_stakeholders(issue, settings)

      if stakeholders.any?
        # Calculate signature box dimensions
        boxes_per_row = 2
        box_width = 190
        box_height = 25

        stakeholders.each_with_index do |stakeholder, index|
          if index % boxes_per_row == 0 && index > 0
            pdf.ln(box_height + 5)
          end
          pdf.ln(5)
          x_offset = 10
          pdf.set_x(x_offset)

          # Signature box
          pdf.SetFontStyle('B', 9)
          pdf.RDMCell(box_width, 6, stakeholder[:role], 'LRT', 1)
          pdf.set_x(x_offset)
          pdf.SetFontStyle('', 8)
          pdf.RDMCell(box_width, 4, stakeholder[:name], 'LR', 1)
          pdf.set_x(x_offset)
          pdf.RDMCell(box_width, 10, '', 'LR', 1)  # Empty space for signature
          pdf.set_x(x_offset)
          pdf.SetFontStyle('', 7)
          pdf.RDMCell(box_width, 4, '签字: ___________      日期: ___________', 'LRB', 1)
        end
      end
    end

    def get_issue_stakeholders(issue, settings)
      stakeholders = []

      # Add role-based stakeholders
      if settings['signature_roles'].present?
        settings['signature_roles'].each do |role_id|
          role = Role.find_by(id: role_id)
          next unless role

          # Find users with this role in the project
          members = issue.project.members.joins(:roles).where(roles: { id: role_id })
          members.each do |member|
            stakeholders << {
              name: member.user.name,
              role: role.name,
              type: 'role'
            }
          end
        end
      end

      # Add custom field based stakeholders
      if settings['custom_signature_fields'].present?
        settings['custom_signature_fields'].each do |field_id|
          custom_field = CustomField.find_by(id: field_id)
          next unless custom_field

          custom_value = issue.custom_field_values.detect { |v| v.custom_field_id == field_id.to_i }
          if custom_value && custom_value.value.present?
            stakeholders << {
              name: custom_value.value,
              role: custom_field.name,
              type: 'custom_field'
            }
          end
        end
      end

      # Always include key project roles if not already added
      default_stakeholders = [
        { user: issue.author, role: l(:label_author, :default => 'Author') },
        { user: issue.assigned_to, role: l(:field_assigned_to) }
      ].compact

      default_stakeholders.each do |stakeholder|
        next unless stakeholder[:user]
        unless stakeholders.any? { |s| s[:name] == stakeholder[:user].name }
          stakeholders << {
            name: stakeholder[:user].name,
            role: stakeholder[:role],
            type: 'default'
          }
        end
      end

      stakeholders.uniq { |s| s[:name] }
    end
  end
end

unless Redmine::Export::PDF::IssuesPdfHelper.included_modules.include?(RedmineCustomPdfExport::PdfCustomizer)
  Redmine::Export::PDF::IssuesPdfHelper.send(:prepend, RedmineCustomPdfExport::PdfCustomizer)
  Rails.logger.info "Successfully patched IssuesPdfHelper with PdfCustomizer"
end
