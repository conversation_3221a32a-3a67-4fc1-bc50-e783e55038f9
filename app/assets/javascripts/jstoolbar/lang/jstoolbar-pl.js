﻿// Keep this line in order to avoid problems with Windows Notepad UTF-8 EF-BB-BF idea...
jsToolBar.strings = {};
jsToolBar.strings['Strong'] = 'Pogrubienie';
jsToolBar.strings['Italic'] = 'Kursywa';
jsToolBar.strings['Underline'] = 'Podkreślenie';
jsToolBar.strings['Deleted'] = 'Usunięte';
jsToolBar.strings['Code'] = 'Kod';
jsToolBar.strings['Heading 1'] = 'Nagłowek 1';
jsToolBar.strings['Heading 2'] = 'Nagłówek 2';
jsToolBar.strings['Heading 3'] = 'Nagłówek 3';
jsToolBar.strings['Highlighted code'] = 'Kod z podświetlaniem składni';
jsToolBar.strings['Unordered list'] = 'Nieposortowana lista';
jsToolBar.strings['Ordered list'] = 'Posortowana lista';
jsToolBar.strings['Quote'] = 'Cytat';
jsToolBar.strings['Unquote'] = 'Usuń cytat';
jsToolBar.strings['Table'] = 'Tabela';
jsToolBar.strings['Preformatted text'] = 'Sformatowany tekst';
jsToolBar.strings['Wiki link'] = 'Odnośnik do strony Wiki';
jsToolBar.strings['Image'] = 'Obraz';
jsToolBar.strings['Edit'] = 'Edytuj';
jsToolBar.strings['Preview'] = 'Podgląd';
