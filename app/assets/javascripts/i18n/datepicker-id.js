/* Indonesian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
/* Fixed by <PERSON> (<EMAIL>) */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.id = {
	closeText: "Tutup",
	prevText: "Mundur",
	nextText: "<PERSON><PERSON>",
	currentText: "Hari ini",
	monthNames: [ "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "April", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON>gus<PERSON>", "September", "Oktober", "Nopember", "Desember" ],
	monthNamesShort: [ "<PERSON>", "Feb", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>" ],
	dayNames: [ "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>mat", "Sabtu" ],
	dayNamesShort: [ "Min", "Sen", "Sel", "Rab", "Kam", "Jum", "Sab" ],
	dayNamesMin: [ "Mg", "Sn", "Sl", "Rb", "Km", "Jm", "Sb" ],
	weekHeader: "Mg",
	dateFormat: "dd/mm/yy",
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.id );

return datepicker.regional.id;

} );
