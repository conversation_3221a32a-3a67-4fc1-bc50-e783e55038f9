/* Galician localization for 'UI date picker' jQuery extension. */
/* Translated by <PERSON> <<EMAIL>>. */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.gl = {
	closeText: "Pechar",
	prevText: "Ant",
	nextText: "Seg",
	currentText: "Hoxe",
	monthNames: [ "<PERSON>ane<PERSON>", "<PERSON>re<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "Agosto", "Setembro", "Outubro", "Novembro", "Decembro" ],
	monthNamesShort: [ "<PERSON><PERSON>", "Feb", "<PERSON>", "<PERSON>br", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>" ],
	dayNames: [ "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>rc<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>bad<PERSON>" ],
	day<PERSON>amesShort: [ "Dom", "Lun", "Mar", "Mér", "Xov", "Ven", "Sáb" ],
	dayNamesMin: [ "Do", "Lu", "Ma", "Mé", "Xo", "Ve", "Sá" ],
	weekHeader: "Sm",
	dateFormat: "dd/mm/yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.gl );

return datepicker.regional.gl;

} );
