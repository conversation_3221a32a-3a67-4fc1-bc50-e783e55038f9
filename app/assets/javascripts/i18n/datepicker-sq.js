/* Albanian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.sq = {
	closeText: "mbylle",
	prevText: "mbrapa",
	nextText: "<PERSON><PERSON>rp<PERSON>",
	currentText: "sot",
	monthNames: [ "<PERSON><PERSON>", "<PERSON>h<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nëntor", "Dhjetor" ],
	monthNamesShort: [ "Jan", "<PERSON>hk", "<PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>h<PERSON>" ],
	dayNames: [ "<PERSON>", "<PERSON>ë<PERSON>", "<PERSON> Martë", "<PERSON> Mërkurë", "<PERSON> Enj<PERSON>", "E Premte", "E Shtune" ],
	dayNamesShort: [ "Di", "Hë", "Ma", "Më", "En", "Pr", "Sh" ],
	dayNamesMin: [ "Di", "Hë", "Ma", "Më", "En", "Pr", "Sh" ],
	weekHeader: "Ja",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.sq );

return datepicker.regional.sq;

} );
