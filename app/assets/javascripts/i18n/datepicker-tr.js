/* Turkish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.tr = {
	closeText: "kapat",
	prevText: "geri",
	nextText: "ileri",
	currentText: "bugün",
	monthNames: [ "<PERSON>cak", "<PERSON>uba<PERSON>", "Mart", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ha<PERSON>ran",
	"Temmuz", "Ağustos", "Eylül", "Ekim", "Kasım", "Aralık" ],
	monthNamesShort: [ "Oca", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "May", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>" ],
	dayNames: [ "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Çarşam<PERSON>", "Perşembe", "Cuma", "Cumartesi" ],
	dayNamesShort: [ "Pz", "Pt", "Sa", "Ça", "Pe", "Cu", "Ct" ],
	dayNamesMin: [ "Pz", "Pt", "Sa", "Ça", "Pe", "Cu", "Ct" ],
	weekHeader: "Hf",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.tr );

return datepicker.regional.tr;

} );
