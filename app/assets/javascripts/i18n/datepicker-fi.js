/* Finnish initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.fi = {
	closeText: "Sulje",
	prevText: "Edellinen",
	nextText: "<PERSON><PERSON><PERSON>",
	currentText: "Tän<PERSON>än",
	monthNames: [ "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>aku<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>" ],
	monthNamesShort: [ "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>" ],
	dayNamesShort: [ "<PERSON>", "<PERSON>", "<PERSON>i", "<PERSON>", "To", "<PERSON>e", "La" ],
	dayNames: [ "Sunnuntai", "Maanantai", "Tii<PERSON>i", "<PERSON><PERSON>viikko", "<PERSON><PERSON>i", "<PERSON>jantai", "<PERSON>antai" ],
	day<PERSON>ames<PERSON>in: [ "<PERSON>", "Ma", "<PERSON>i", "<PERSON>", "To", "<PERSON>e", "<PERSON>" ],
	week<PERSON><PERSON>er: "Vk",
	dateFormat: "d.m.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.fi );

return datepicker.regional.fi;

} );
