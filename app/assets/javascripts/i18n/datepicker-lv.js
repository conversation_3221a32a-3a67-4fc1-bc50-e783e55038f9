/* Latvian (UTF-8) initialisation for the jQuery UI date picker plugin. */
/* <AUTHOR> <<EMAIL>> */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.lv = {
	closeText: "Aizvērt",
	prevText: "Iepr.",
	nextText: "Nāk.",
	currentText: "<PERSON><PERSON><PERSON>",
	monthNames: [ "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON><PERSON><PERSON>", "Augusts", "Septembris", "Oktobri<PERSON>", "Novembris", "Decembris" ],
	monthNamesShort: [ "<PERSON>", "<PERSON>", "<PERSON>", "Apr", "<PERSON>", "<PERSON><PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "Okt", "Nov", "<PERSON>" ],
	dayNames: [
		"svētdiena",
		"pirmdiena",
		"otrdiena",
		"trešdiena",
		"ceturtdiena",
		"piektdiena",
		"sestdiena"
	],
	dayNamesShort: [ "svt", "prm", "otr", "tre", "ctr", "pkt", "sst" ],
	dayNamesMin: [ "Sv", "Pr", "Ot", "Tr", "Ct", "Pk", "Ss" ],
	weekHeader: "Ned.",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.lv );

return datepicker.regional.lv;

} );
