/* Estonian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON> (mrts.pydev at gmail com). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.et = {
	closeText: "Sulge",
	prevText: "Eelnev",
	nextText: "Järgnev",
	currentText: "Täna",
	monthNames: [ "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "April<PERSON>", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "August", "September", "Oktoober", "November", "Detsember" ],
	monthNamesShort: [ "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON>", "Sept", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>" ],
	dayNames: [
		"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON><PERSON>",
		"<PERSON><PERSON><PERSON><PERSON><PERSON>",
		"<PERSON><PERSON>jap<PERSON>ev",
		"<PERSON>e",
		"<PERSON>p<PERSON>ev"
	],
	dayNamesShort: [ "Pühap", "Esmasp", "Teisip", "Kolmap", "Neljap", "Reede", "Laup" ],
	dayNamesMin: [ "P", "E", "T", "K", "N", "R", "L" ],
	weekHeader: "näd",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.et );

return datepicker.regional.et;

} );
