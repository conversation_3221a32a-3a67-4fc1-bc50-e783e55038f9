/* Romanian initialisation for the jQuery UI date picker plugin.
 *
 * Written by <PERSON> (<EMAIL>)
 * and <PERSON><PERSON> (<EMAIL>)
 */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.ro = {
	closeText: "Închide",
	prevText: "Luna precedentă",
	nextText: "<PERSON> următoare ",
	currentText: "<PERSON><PERSON>",
	monthNames: [ "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>",
	"<PERSON>uli<PERSON>", "August", "Septembrie", "Octombrie", "<PERSON>ie<PERSON><PERSON>", "Decembrie" ],
	monthNamesShort: [ "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON>", "<PERSON>", "Oct", "Nov", "<PERSON>" ],
	dayNames: [ "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>i", "<PERSON><PERSON>", "S<PERSON>mb<PERSON>t<PERSON>" ],
	dayNamesShort: [ "Dum", "Lun", "Mar", "Mie", "Joi", "Vin", "Sâm" ],
	dayNamesMin: [ "Du", "Lu", "Ma", "Mi", "Jo", "Vi", "Sâ" ],
	weekHeader: "<PERSON>ăpt",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.ro );

return datepicker.regional.ro;

} );
