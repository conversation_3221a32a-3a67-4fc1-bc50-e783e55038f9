/* Portuguese initialisation for the jQuery UI date picker plugin. */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.pt = {
	closeText: "Fechar",
	prevText: "Anterior",
	nextText: "Seguinte",
	currentText: "Hoje",
	monthNames: [ "Janeiro", "Fevereiro", "Março", "Abril", "Maio", "Jun<PERSON>",
	"Julho", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro" ],
	monthNamesShort: [ "Jan", "Fev", "Mar", "Abr", "Mai", "Jun",
	"Jul", "<PERSON><PERSON>", "Set", "Out", "Nov", "Dez" ],
	dayNames: [
		"<PERSON>",
		"<PERSON>-feira",
		"<PERSON><PERSON><PERSON><PERSON>feira",
		"<PERSON><PERSON><PERSON>-f<PERSON>",
		"<PERSON>uin<PERSON>-feira",
		"<PERSON><PERSON>-feira",
		"<PERSON><PERSON><PERSON><PERSON>"
	],
	dayNamesShort: [ "<PERSON>", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb" ],
	dayNamesMin: [ "Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb" ],
	weekHeader: "Sem",
	dateFormat: "dd/mm/yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.pt );

return datepicker.regional.pt;

} );
