/* Bosnian i18n for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON>. */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.bs = {
	closeText: "Zatvori",
	prevText: "Prethodno",
	nextText: "Sljedeći",
	currentText: "Danas",
	monthNames: [ "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>ar", "<PERSON>", "April", "<PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "August", "Septembar", "Oktobar", "Novembar", "Decembar" ],
	monthNamesShort: [ "Jan", "Feb", "Mar", "Apr", "Maj", "Jun",
	"Jul", "Aug", "Sep", "Okt", "Nov", "Dec" ],
	dayNames: [ "<PERSON><PERSON><PERSON>", "<PERSON>ned<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>etvrta<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>" ],
	dayNamesShort: [ "<PERSON>", "Pon", "Uto", "Sri", "Čet", "Pet", "Sub" ],
	dayNamesMin: [ "Ne", "Po", "Ut", "Sr", "Če", "Pe", "Su" ],
	weekHeader: "Wk",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.bs );

return datepicker.regional.bs;

} );
