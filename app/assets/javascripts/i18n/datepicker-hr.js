/* Croatian i18n for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON><PERSON>. */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.hr = {
	closeText: "Zatvori",
	prevText: "Prethodno",
	nextText: "Sljedeć<PERSON>",
	currentText: "Danas",
	monthNames: [ "Siječanj", "Veljača", "Ožujak", "Travanj", "Svibanj", "Lipanj",
	"Srpanj", "Kolovoz", "Rujan", "Listopad", "Studeni", "Prosinac" ],
	monthNamesShort: [ "Sij", "<PERSON>elj", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>",
	"<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>" ],
	dayNames: [ "<PERSON><PERSON><PERSON><PERSON>", "Ponedjeljak", "Utorak", "Srije<PERSON>", "Četvrtak", "Petak", "Subota" ],
	dayNamesShort: [ "Ned", "Pon", "Uto", "Sri", "Čet", "Pet", "Sub" ],
	dayNamesMin: [ "Ne", "Po", "Ut", "Sr", "Če", "Pe", "Su" ],
	weekHeader: "Tje",
	dateFormat: "dd.mm.yy.",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.hr );

return datepicker.regional.hr;

} );
