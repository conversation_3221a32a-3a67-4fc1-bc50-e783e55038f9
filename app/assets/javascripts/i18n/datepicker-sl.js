/* Slovenian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
/* c = č, s = š z = ž C = Č S = Š Z = Ž */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.sl = {
	closeText: "<PERSON>ap<PERSON>",
	prevText: "Prejš<PERSON>",
	nextText: "Naslednji",
	currentText: "Trenutni",
	monthNames: [ "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "April", "<PERSON>", "<PERSON>ij",
	"Julij", "Avgust", "September", "Oktober", "November", "December" ],
	monthNamesShort: [ "<PERSON>", "<PERSON>", "<PERSON>", "Apr", "<PERSON>", "<PERSON>",
	"<PERSON>", "Avg", "<PERSON>", "Okt", "Nov", "<PERSON>" ],
	dayNames: [ "<PERSON><PERSON><PERSON>", "Ponedeljek", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>etrtek", "Petek", "Sobota" ],
	dayNamesShort: [ "Ned", "Pon", "Tor", "Sre", "Čet", "Pet", "Sob" ],
	dayNamesMin: [ "Ne", "Po", "To", "Sr", "Če", "Pe", "So" ],
	weekHeader: "Teden",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.sl );

return datepicker.regional.sl;

} );
