/* Serbian i18n for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON>. */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.sr = {
	closeText: "Затвори",
	prevText: "Претходна",
	nextText: "Следећи",
	currentText: "Данас",
	monthNames: [ "<PERSON>а<PERSON><PERSON><PERSON><PERSON>", "Фебру<PERSON>р", "Март", "А<PERSON>рил", "Мај", "Јун",
	"Јул", "Август", "Септембар", "Октобар", "Новембар", "Децембар" ],
	monthNamesShort: [ "<PERSON>а<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>пр", "<PERSON>а<PERSON>", "<PERSON>у<PERSON>",
	"<PERSON>у<PERSON>", "Авг", "<PERSON>е<PERSON>", "<PERSON>к<PERSON>", "<PERSON><PERSON>", "Дец" ],
	dayNames: [ "Недеља", "Понедељак", "Уторак", "Среда", "Четвртак", "Петак", "Субота" ],
	dayNamesShort: [ "Нед", "Пон", "Уто", "Сре", "Чет", "Пет", "Суб" ],
	dayNamesMin: [ "Не", "По", "Ут", "Ср", "Че", "Пе", "Су" ],
	weekHeader: "Сед",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.sr );

return datepicker.regional.sr;

} );
