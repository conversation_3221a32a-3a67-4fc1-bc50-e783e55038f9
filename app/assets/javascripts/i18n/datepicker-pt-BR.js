/* Brazilian initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional[ "pt-BR" ] = {
	closeText: "Fechar",
	prevText: "Anterior",
	nextText: "Próximo",
	currentText: "Hoje",
	monthNames: [ "Janeiro", "Fevereiro", "Março", "Abril", "<PERSON><PERSON>", "<PERSON><PERSON>",
	"Jul<PERSON>", "Agosto", "Setembro", "Outubro", "Novembro", "Dezembro" ],
	monthNamesShort: [ "<PERSON>", "<PERSON>v", "<PERSON>", "<PERSON>b<PERSON>", "<PERSON>", "<PERSON>",
	"<PERSON>", "<PERSON><PERSON>", "<PERSON>", "Out", "Nov", "<PERSON><PERSON>" ],
	dayNames: [
		"<PERSON>",
		"<PERSON>-feira",
		"<PERSON>r<PERSON>-feira",
		"<PERSON>uarta-feira",
		"<PERSON>uinta-feira",
		"Sexta-feira",
		"Sábado"
	],
	dayNamesShort: [ "Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb" ],
	dayNamesMin: [ "Dom", "Seg", "Ter", "Qua", "Qui", "Sex", "Sáb" ],
	weekHeader: "Sm",
	dateFormat: "dd/mm/yy",
	firstDay: 0,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional[ "pt-BR" ] );

return datepicker.regional[ "pt-BR" ];

} );
