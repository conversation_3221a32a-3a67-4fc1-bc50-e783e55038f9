/* Azerbaijani (UTF-8) initialisation for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON> (<EMAIL>). */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.az = {
	closeText: "Bağla",
	prevText: "<PERSON><PERSON>",
	nextText: "<PERSON>rə<PERSON>",
	currentText: "<PERSON>ugün",
	monthNames: [ "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>el", "May", "<PERSON><PERSON>",
	"<PERSON>yul", "Avqust", "Sentyabr", "Oktyabr", "Noyabr", "Dekabr" ],
	monthNamesShort: [ "<PERSON>", "<PERSON>v", "<PERSON>", "Apr", "May", "<PERSON><PERSON>",
	"<PERSON><PERSON><PERSON>", "<PERSON>v<PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "De<PERSON>" ],
	dayNames: [ "<PERSON><PERSON>", "<PERSON><PERSON> ertəsi", "<PERSON><PERSON><PERSON><PERSON>ənbə axşamı", "Çərşənbə", "Cümə axşamı", "Cümə", "Şənbə" ],
	dayNamesShort: [ "B", "Be", "Ça", "Ç", "Ca", "C", "Ş" ],
	dayNamesMin: [ "B", "B", "Ç", "С", "Ç", "C", "Ş" ],
	weekHeader: "Hf",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.az );

return datepicker.regional.az;

} );
