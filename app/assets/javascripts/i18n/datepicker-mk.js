/* Macedonian i18n for the jQuery UI date picker plugin. */
/* Written by <PERSON><PERSON><PERSON><PERSON>. */
( function( factory ) {
	"use strict";

	if ( typeof define === "function" && define.amd ) {

		// AMD. Register as an anonymous module.
		define( [ "../widgets/datepicker" ], factory );
	} else {

		// Browser globals
		factory( jQuery.datepicker );
	}
} )( function( datepicker ) {
"use strict";

datepicker.regional.mk = {
	closeText: "Затвори",
	prevText: "Претходна",
	nextText: "Следно",
	currentText: "Денес",
	monthNames: [ "Јануари", "Февруари", "Мар<PERSON>", "А<PERSON>рил", "Мај", "Јуни",
	"Јули", "Август", "Септември", "Октомври", "Ноември", "Декември" ],
	monthNamesShort: [ "<PERSON>ан", "<PERSON>ев", "Мар", "<PERSON><PERSON>р", "<PERSON>а<PERSON>", "<PERSON>у<PERSON>",
	"Јул", "Авг", "<PERSON>е<PERSON>", "Окт", "Ное", "Дек" ],
	dayNames: [ "Недела", "Понеделник", "Вторник", "Среда", "Четврток", "Петок", "Сабота" ],
	dayNamesShort: [ "Нед", "Пон", "Вто", "Сре", "Чет", "Пет", "Саб" ],
	dayNamesMin: [ "Не", "По", "Вт", "Ср", "Че", "Пе", "Са" ],
	weekHeader: "Сед",
	dateFormat: "dd.mm.yy",
	firstDay: 1,
	isRTL: false,
	showMonthAfterYear: false,
	yearSuffix: "" };
datepicker.setDefaults( datepicker.regional.mk );

return datepicker.regional.mk;

} );
